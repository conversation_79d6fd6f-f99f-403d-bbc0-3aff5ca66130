{"name": "anythingllm-connector", "displayName": "AnythingLLM Connector", "description": "Geavanceerde integratie met AnythingLLM voor werkruimtes, chats, documenten en agent training", "version": "1.0.0", "publisher": "innovars", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Extension Packs"], "keywords": ["anythingllm", "ai", "llm", "chat", "workspace", "documents", "agents"], "activationEvents": ["onCommand:anythingllm.workspace.list", "onCommand:anythingllm.workspace.create", "onCommand:anythingllm.document.upload", "onCommand:anythingllm.chat.start", "onCommand:anythingllm.chat.message", "onCommand:anythingllm.agent.create", "onCommand:anythingllm.agent.list"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "anythingllm.workspace.list", "title": "Toon alle werkruimtes", "category": "AnythingLLM"}, {"command": "anythingllm.workspace.create", "title": "<PERSON><PERSON> nieuwe werkruimte", "category": "AnythingLLM"}, {"command": "anythingllm.document.upload", "title": "Upload document naar werkruimte", "category": "AnythingLLM"}, {"command": "anythingllm.chat.start", "title": "Start nieuwe chat in werkruimte", "category": "AnythingLLM"}, {"command": "anythingllm.chat.message", "title": "<PERSON><PERSON><PERSON> bericht naar werkruimte chat", "category": "AnythingLLM"}, {"command": "anythingllm.agent.create", "title": "<PERSON><PERSON> en train nieuwe agent", "category": "AnythingLLM"}, {"command": "anythingllm.agent.list", "title": "Toon alle agents", "category": "AnythingLLM"}], "configuration": {"title": "AnythingLLM Connector", "properties": {"anythingllm.apiUrl": {"type": "string", "default": "http://localhost:3001", "description": "AnythingLLM API URL", "scope": "window"}, "anythingllm.apiKey": {"type": "string", "default": "1CYG2RE-H8NMEGM-G1M411T-3YQS0JD", "description": "AnythingLLM API Key", "scope": "window"}}}, "menus": {"commandPalette": [{"command": "anythingllm.workspace.list", "when": "true"}, {"command": "anythingllm.workspace.create", "when": "true"}, {"command": "anythingllm.document.upload", "when": "true"}, {"command": "anythingllm.chat.start", "when": "true"}, {"command": "anythingllm.chat.message", "when": "true"}, {"command": "anythingllm.agent.create", "when": "true"}, {"command": "anythingllm.agent.list", "when": "true"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"node-fetch": "^3.3.0", "ws": "^8.13.0", "form-data": "^4.0.0"}}