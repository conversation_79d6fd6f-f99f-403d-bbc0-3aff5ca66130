{"name": "AnythingLLM Connector", "version": "1.0.0", "description": "Geavanceerde integratie met AnythingLLM, inclusief werkruimtes, chats, documenten en agent training", "main": "main.js", "author": "Innovars", "commands": [{"name": "anythingllm.workspace.list", "description": "Toon alle werkruimtes", "execution_thread": "background"}, {"name": "anythingllm.workspace.create", "description": "Maak een nieuwe werkruimte", "execution_thread": "background"}, {"name": "anythingllm.document.upload", "description": "Upload document naar werkruimte", "execution_thread": "background"}, {"name": "anythingllm.chat.start", "description": "Start een nieuwe chat in werkruimte", "execution_thread": "background"}, {"name": "anythingllm.chat.message", "description": "<PERSON><PERSON><PERSON> bericht naar werkruimte chat", "execution_thread": "background"}, {"name": "anythingllm.agent.create", "description": "Maak en train een nieuwe agent", "execution_thread": "background"}, {"name": "anythingllm.agent.list", "description": "Toon alle agents", "execution_thread": "background"}], "permissions": ["http", "file-system", "clipboard"], "config": {"apiUrl": {"type": "string", "default": "http://localhost:3001", "description": "AnythingLLM API URL"}, "apiKey": {"type": "string", "default": "1CYG2RE-H8NMEGM-G1M411T-3YQS0JD", "description": "AnythingLLM API Key"}}}